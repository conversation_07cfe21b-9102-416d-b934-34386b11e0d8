// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zpsujubgenvmzzetfpac.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpwc3VqdWJnZW52bXp6ZXRmcGFjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1NDg5ODQsImV4cCI6MjA2OTEyNDk4NH0.TARw4UQ2nxPD7qIZpiHv3cpDVqR2OmXcJce6RbjecWs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});