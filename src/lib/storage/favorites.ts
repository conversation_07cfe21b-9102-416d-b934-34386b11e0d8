import { STORAGE_KEYS } from "@/constants";

// Favorites utility functions
export const getFavorites = (): string[] => {
  const favorites = localStorage.getItem(STORAGE_KEYS.FAVORITES);
  return favorites ? JSON.parse(favorites) : [];
};

export const addToFavorites = (offerId: string): void => {
  const favorites = getFavorites();
  if (!favorites.includes(offerId)) {
    favorites.push(offerId);
    localStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(favorites));
  }
};

export const removeFromFavorites = (offerId: string): void => {
  const favorites = getFavorites();
  const updated = favorites.filter((id) => id !== offerId);
  localStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(updated));
};

export const isFavorite = (offerId: string): boolean => {
  return getFavorites().includes(offerId);
};
