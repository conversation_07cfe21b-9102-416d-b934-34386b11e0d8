import { type Offer } from "@/hooks/useOffers";

// CSV Export utility
export function exportToCSV(offers: Offer[], filename: string = "offers") {
  const headers = [
    "Operator",
    "Title",
    "Data Amount",
    "Minutes",
    "Validity (Days)",
    "Selling Price",
    "Original Price",
    "Region",
    "Category",
    "WhatsApp Number",
  ];

  const csvContent = [
    headers.join(","),
    ...offers.map((offer) =>
      [
        offer.operator,
        `"${offer.title}"`,
        offer.data_amount,
        offer.minutes,
        offer.validity_days,
        offer.selling_price,
        offer.original_price,
        offer.region,
        offer.category,
        offer.whatsapp_number,
      ].join(","),
    ),
  ].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.csv`,
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
