// Offer-related types
export interface Offer {
  id: string;
  operator: string;
  title: string;
  data_amount: string;
  minutes: number;
  validity_days: number;
  selling_price: number;
  original_price: number;
  region: string;
  category: string;
  whatsapp_number: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ParsedOffer {
  operator: string;
  title: string;
  data_amount: string;
  minutes: number;
  validity_days: number;
  selling_price: number;
  original_price?: number;
  region?: string;
  category?: string;
  whatsapp_number?: string;
  description?: string;
}

export interface ValidationError {
  row: number;
  field: string;
  message: string;
  value: any;
}
