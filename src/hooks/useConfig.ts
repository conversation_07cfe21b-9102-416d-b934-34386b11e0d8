import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { type ConfigData } from '@/types';

export const useConfig = () => {
  const [config, setConfig] = useState<ConfigData>({
    operators: [],
    categories: [],
    default_whatsapp: '',
    support_phone: '',
    support_email: '',
    company_name: '',
  });
  const [isLoading, setIsLoading] = useState(true);

  const fetchConfig = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('config')
        .select('key, value');

      if (error) {
        throw error;
      }

      const configMap: any = {};
      data?.forEach(item => {
        configMap[item.key] = item.value;
      });

      setConfig(configMap);
    } catch (err) {
      console.error('Failed to fetch config:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  return {
    config,
    isLoading,
    refreshConfig: fetchConfig,
  };
};